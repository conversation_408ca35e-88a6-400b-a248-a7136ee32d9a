1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.cat_player"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="35" />
9-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission
12-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:4:5-80
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:4:22-77
14        android:maxSdkVersion="32" />
14-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-35
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:5:5-81
15-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:5:22-78
16    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:6:5-82
16-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:6:22-79
17
18    <queries>
18-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-19:15
19        <intent>
19-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-18:18
20            <action android:name="android.intent.action.GET_CONTENT" />
20-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-72
20-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:15:21-69
21
22            <data android:mimeType="*/*" />
22-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-44
22-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:17:19-41
23        </intent>
24    </queries>
25
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->[androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\2e13b545bd7d68ad8e1860bdd0b1eee9\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:5-79
26-->[androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\2e13b545bd7d68ad8e1860bdd0b1eee9\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:22-76
27
28    <permission
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\95fdb6225b6e552a46d9596694dbe4fc\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
29        android:name="com.example.cat_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\95fdb6225b6e552a46d9596694dbe4fc\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\95fdb6225b6e552a46d9596694dbe4fc\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.example.cat_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\95fdb6225b6e552a46d9596694dbe4fc\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\95fdb6225b6e552a46d9596694dbe4fc\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
33
34    <application
35        android:name="android.app.Application"
35-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:10:9-42
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\95fdb6225b6e552a46d9596694dbe4fc\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
37        android:debuggable="true"
38        android:icon="@mipmap/ic_launcher"
38-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:11:9-43
39        android:label="Cat Player" >
39-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:9:9-35
40        <activity
40-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:12:9-28:20
41            android:name="com.example.cat_player.MainActivity"
41-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:13:13-41
42            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
42-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:17:13-163
43            android:exported="true"
43-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:14:13-36
44            android:hardwareAccelerated="true"
44-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:18:13-47
45            android:launchMode="singleTop"
45-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:15:13-43
46            android:theme="@style/LaunchTheme"
46-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:16:13-47
47            android:windowSoftInputMode="adjustResize" >
47-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:19:13-55
48            <meta-data
48-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:20:13-23:17
49                android:name="io.flutter.embedding.android.NormalTheme"
49-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:21:15-70
50                android:resource="@style/NormalTheme" />
50-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:22:15-52
51
52            <intent-filter android:autoVerify="true" >
52-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:24:13-27:29
52-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:24:28-53
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:25:17-68
53-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:25:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:26:17-76
55-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:26:27-74
56            </intent-filter>
57        </activity>
58
59        <meta-data
59-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:29:9-31:33
60            android:name="flutterEmbedding"
60-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:30:13-44
61            android:value="2" />
61-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:31:13-30
62
63        <uses-library
63-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cab53fbfe8f9ac8c3e5191d54f0a401f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
64            android:name="androidx.window.extensions"
64-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cab53fbfe8f9ac8c3e5191d54f0a401f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
65            android:required="false" />
65-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cab53fbfe8f9ac8c3e5191d54f0a401f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
66        <uses-library
66-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cab53fbfe8f9ac8c3e5191d54f0a401f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
67            android:name="androidx.window.sidecar"
67-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cab53fbfe8f9ac8c3e5191d54f0a401f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
68            android:required="false" />
68-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\cab53fbfe8f9ac8c3e5191d54f0a401f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
69
70        <provider
70-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ec297cfaf86dba29bf9416181331b02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
71            android:name="androidx.startup.InitializationProvider"
71-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ec297cfaf86dba29bf9416181331b02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
72            android:authorities="com.example.cat_player.androidx-startup"
72-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ec297cfaf86dba29bf9416181331b02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
73            android:exported="false" >
73-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ec297cfaf86dba29bf9416181331b02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
74            <meta-data
74-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ec297cfaf86dba29bf9416181331b02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
75-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ec297cfaf86dba29bf9416181331b02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
76                android:value="androidx.startup" />
76-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ec297cfaf86dba29bf9416181331b02\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <receiver
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
83            android:name="androidx.profileinstaller.ProfileInstallReceiver"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
84            android:directBootAware="false"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
85            android:enabled="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
86            android:exported="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
87            android:permission="android.permission.DUMP" >
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
89                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
92                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
95                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
98                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\e4f0aa4613aba93d8bcdaa8c0de1e857\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
99            </intent-filter>
100        </receiver>
101    </application>
102
103</manifest>
