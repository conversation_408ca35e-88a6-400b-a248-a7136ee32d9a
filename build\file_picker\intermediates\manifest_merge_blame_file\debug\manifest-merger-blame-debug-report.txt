1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mr.flutter.plugin.filepicker" >
4
5    <uses-sdk
6        android:minSdkVersion="16"
6-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml
7        android:targetSdkVersion="16" />
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml
8
9    <uses-permission
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:3:5-106
10        android:name="android.permission.READ_EXTERNAL_STORAGE"
10-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:3:22-77
11        android:maxSdkVersion="32" />
11-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:3:78-104
12
13    <queries>
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:4:5-9:15
14        <intent>
14-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:5:6-8:14
15            <action android:name="android.intent.action.GET_CONTENT" />
15-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:6:9-68
15-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:6:17-65
16
17            <data android:mimeType="*/*" />
17-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:7:9-39
17-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-6.2.1\android\src\main\AndroidManifest.xml:7:15-37
18        </intent>
19    </queries>
20
21</manifest>
