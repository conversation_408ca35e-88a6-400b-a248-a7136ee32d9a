{"logs": [{"outputFile": "com.example.cat_player.app-mergeDebugResources-40:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\61778a0f7aa3fa286a534061d48ac657\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "29,70,71,84,85,108,109,209,210,211,212,213,214,215,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,306,307,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,352,381,382,383,384,385,386,387,404,1926,1927,1931,1932,1936,2079,2080", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,3841,3906,5325,5394,12244,12314,12382,12454,12524,12585,12659,13902,13963,14024,14086,14150,14212,14273,14341,14441,14501,14567,14640,14709,14766,14818,15766,15838,15914,15979,16038,16097,16157,16217,16277,16337,16397,16457,16517,16577,16637,16697,16756,16816,16876,16936,16996,17056,17116,17176,17236,17296,17356,17415,17475,17535,17594,17653,17712,17771,17830,18398,18433,19019,19074,19137,19192,19250,19308,19369,19432,19489,19540,19590,19651,19708,19774,19808,19843,20816,22835,22902,22974,23043,23112,23186,23258,24324,123353,123470,123671,123781,123982,135354,135426", "endLines": "29,70,71,84,85,108,109,209,210,211,212,213,214,215,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,306,307,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,352,381,382,383,384,385,386,387,404,1926,1930,1931,1935,1936,2079,2080", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "1040,2788,2876,3901,3967,5389,5452,12309,12377,12449,12519,12580,12654,12727,13958,14019,14081,14145,14207,14268,14336,14436,14496,14562,14635,14704,14761,14813,14875,15833,15909,15974,16033,16092,16152,16212,16272,16332,16392,16452,16512,16572,16632,16692,16751,16811,16871,16931,16991,17051,17111,17171,17231,17291,17351,17410,17470,17530,17589,17648,17707,17766,17825,17884,18428,18463,19069,19132,19187,19245,19303,19364,19427,19484,19535,19585,19646,19703,19769,19803,19838,19873,20881,22897,22969,23038,23107,23181,23253,23341,24390,123465,123666,123776,123977,124106,135421,135488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\937e01ffe8e3e44dd70443b1b5c16c96\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "301,317,345", "startColumns": "4,4,4", "startOffsets": "18154,18908,20383", "endColumns": "56,64,63", "endOffsets": "18206,18968,20442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4a74e5ac7357230d5a0fd7c6e51811c9\\transformed\\jetified-lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "344", "startColumns": "4", "startOffsets": "20333", "endColumns": "49", "endOffsets": "20378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8b30fecce617e5aea6acd97724cab18f\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,300", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,18094", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,300", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,18149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9818094924ae8d5efe0adc37cab3bd38\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "380", "startColumns": "4", "startOffsets": "22752", "endColumns": "82", "endOffsets": "22830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\51e2db322baefcf987b45c52f853c125\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "218,219,220,228,229,230,305", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12905,12964,13012,13679,13754,13830,18332", "endColumns": "58,47,55,74,75,71,65", "endOffsets": "12959,13007,13063,13749,13825,13897,18393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f3570528e38cbbfd6737e6cd70922de8\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "302,303,308,315,316,335,336,337,338,339", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "18211,18251,18468,18806,18861,19878,19932,19984,20033,20094", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "18246,18293,18506,18856,18903,19927,19979,20028,20089,20139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\78555cd4f200d0089ab1a65fd6f62ef1\\transformed\\jetified-lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "341", "startColumns": "4", "startOffsets": "20176", "endColumns": "42", "endOffsets": "20214"}}, {"source": "C:\\Users\\<USER>\\Desktop\\New folder (2)\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,11", "startColumns": "4,4", "startOffsets": "137,634", "endLines": "7,13", "endColumns": "12,12", "endOffsets": "433,800"}, "to": {"startLines": "1497,1501", "startColumns": "4,4", "startOffsets": "94228,94409", "endLines": "1500,1503", "endColumns": "12,12", "endOffsets": "94404,94573"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\37e60f48f83182062f6e03d9950badc4\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,82,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,216,217,221,222,223,224,225,226,227,253,254,255,256,257,258,259,260,296,297,298,299,304,312,313,318,340,346,347,348,349,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,403,408,409,410,411,412,413,421,422,426,430,434,439,445,452,456,460,465,469,473,477,481,485,489,495,499,505,509,515,519,524,528,531,535,541,545,551,555,561,564,568,572,576,580,584,585,586,587,590,593,596,599,603,604,605,606,607,610,612,614,616,621,622,626,632,636,637,639,650,651,655,661,665,666,667,671,698,702,703,707,735,905,931,1102,1128,1159,1167,1173,1187,1209,1214,1219,1229,1238,1247,1251,1258,1266,1273,1274,1283,1286,1289,1293,1297,1301,1304,1305,1310,1315,1325,1330,1337,1343,1344,1347,1351,1356,1358,1360,1363,1366,1368,1372,1375,1382,1385,1388,1392,1394,1398,1400,1402,1404,1408,1416,1424,1436,1442,1451,1454,1465,1468,1469,1474,1475,1504,1573,1643,1644,1654,1663,1815,1817,1821,1824,1827,1830,1833,1836,1839,1842,1846,1849,1852,1855,1859,1862,1866,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1892,1894,1895,1896,1897,1898,1899,1900,1901,1903,1904,1906,1907,1909,1911,1912,1914,1915,1916,1917,1918,1919,1921,1922,1923,1924,1925,1937,1939,1941,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1957,1958,1959,1960,1961,1962,1964,1968,1972,1973,1974,1975,1976,1977,1981,1982,1983,1984,1986,1988,1990,1992,1994,1995,1996,1997,1999,2001,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2017,2018,2019,2020,2022,2024,2025,2027,2028,2030,2032,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2047,2048,2049,2050,2052,2053,2054,2055,2056,2058,2060,2062,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3726,3783,3972,4046,4121,4186,4252,4312,4373,4445,4518,4585,4653,4712,4771,4830,4889,4948,5002,5056,5109,5163,5217,5271,5526,5600,5679,5752,5826,5897,5969,6041,6114,6171,6229,6302,6376,6450,6525,6597,6670,6740,6811,6871,6932,7001,7070,7140,7214,7290,7354,7431,7507,7584,7649,7718,7795,7870,7939,8007,8084,8150,8211,8308,8373,8442,8541,8612,8671,8729,8786,8845,8909,8980,9052,9124,9196,9268,9335,9403,9471,9530,9593,9657,9747,9838,9898,9964,10031,10097,10167,10231,10284,10351,10412,10479,10592,10650,10713,10778,10843,10918,10991,11063,11112,11173,11234,11295,11357,11421,11485,11549,11614,11677,11737,11798,11864,11923,11983,12045,12116,12176,12732,12818,13068,13158,13245,13333,13415,13498,13588,15313,15365,15423,15468,15534,15598,15655,15712,17889,17946,17994,18043,18298,18668,18715,18973,20144,20447,20511,20573,20633,20886,20960,21030,21108,21162,21232,21317,21365,21411,21472,21535,21601,21665,21736,21799,21864,21928,21989,22050,22102,22175,22249,22318,22393,22467,22541,22682,24271,24632,24710,24800,24888,24984,25074,25656,25745,25992,26273,26525,26810,27203,27680,27902,28124,28400,28627,28857,29087,29317,29547,29774,30193,30419,30844,31074,31502,31721,32004,32212,32343,32570,32996,33221,33648,33869,34294,34414,34690,34991,35315,35606,35920,36057,36188,36293,36535,36702,36906,37114,37385,37497,37609,37714,37831,38045,38191,38331,38417,38765,38853,39099,39517,39766,39848,39946,40538,40638,40890,41314,41569,41663,41752,41989,44013,44255,44357,44610,46766,57298,58814,69445,70973,72730,73356,73776,74837,76102,76358,76594,77141,77635,78240,78438,79018,79582,79957,80075,80613,80770,80966,81239,81495,81665,81806,81870,82235,82602,83278,83542,83880,84233,84327,84513,84819,85081,85206,85333,85572,85783,85902,86095,86272,86727,86908,87030,87289,87402,87589,87691,87798,87927,88202,88710,89206,90083,90377,90947,91096,91828,92000,92084,92420,92512,94578,99824,105213,105275,105853,106437,114384,114497,114726,114886,115038,115209,115375,115544,115711,115874,116117,116287,116460,116631,116905,117104,117309,117639,117723,117819,117915,118013,118113,118215,118317,118419,118521,118623,118723,118819,118931,119060,119183,119314,119445,119543,119657,119751,119891,120025,120121,120233,120333,120449,120545,120657,120757,120897,121033,121197,121327,121485,121635,121776,121920,122055,122167,122317,122445,122573,122709,122841,122971,123101,123213,124111,124257,124401,124539,124605,124695,124771,124875,124965,125067,125175,125283,125383,125463,125555,125653,125763,125841,125947,126039,126143,126253,126375,126538,126695,126775,126875,126965,127075,127165,127406,127500,127606,127698,127798,127910,128024,128140,128256,128350,128464,128576,128678,128798,128920,129002,129106,129226,129352,129450,129544,129632,129744,129860,129982,130094,130269,130385,130471,130563,130675,130799,130866,130992,131060,131188,131332,131460,131529,131624,131739,131852,131951,132060,132171,132282,132383,132488,132588,132718,132809,132932,133026,133138,133224,133328,133424,133512,133630,133734,133838,133964,134052,134160,134260,134350,134460,134544,134646,134730,134784,134848,134954,135040,135150,135234", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,82,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,216,217,221,222,223,224,225,226,227,253,254,255,256,257,258,259,260,296,297,298,299,304,312,313,318,340,346,347,348,349,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,403,408,409,410,411,412,420,421,425,429,433,438,444,451,455,459,464,468,472,476,480,484,488,494,498,504,508,514,518,523,527,530,534,540,544,550,554,560,563,567,571,575,579,583,584,585,586,589,592,595,598,602,603,604,605,606,609,611,613,615,620,621,625,631,635,636,638,649,650,654,660,664,665,666,670,697,701,702,706,734,904,930,1101,1127,1158,1166,1172,1186,1208,1213,1218,1228,1237,1246,1250,1257,1265,1272,1273,1282,1285,1288,1292,1296,1300,1303,1304,1309,1314,1324,1329,1336,1342,1343,1346,1350,1355,1357,1359,1362,1365,1367,1371,1374,1381,1384,1387,1391,1393,1397,1399,1401,1403,1407,1415,1423,1435,1441,1450,1453,1464,1467,1468,1473,1474,1479,1572,1642,1643,1653,1662,1663,1816,1820,1823,1826,1829,1832,1835,1838,1841,1845,1848,1851,1854,1858,1861,1865,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1891,1893,1894,1895,1896,1897,1898,1899,1900,1902,1903,1905,1906,1908,1910,1911,1913,1914,1915,1916,1917,1918,1920,1921,1922,1923,1924,1925,1938,1940,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1956,1957,1958,1959,1960,1961,1963,1967,1971,1972,1973,1974,1975,1976,1980,1981,1982,1983,1985,1987,1989,1991,1993,1994,1995,1996,1998,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2016,2017,2018,2019,2021,2023,2024,2026,2027,2029,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2046,2047,2048,2049,2051,2052,2053,2054,2055,2057,2059,2061,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,3778,3836,4041,4116,4181,4247,4307,4368,4440,4513,4580,4648,4707,4766,4825,4884,4943,4997,5051,5104,5158,5212,5266,5320,5595,5674,5747,5821,5892,5964,6036,6109,6166,6224,6297,6371,6445,6520,6592,6665,6735,6806,6866,6927,6996,7065,7135,7209,7285,7349,7426,7502,7579,7644,7713,7790,7865,7934,8002,8079,8145,8206,8303,8368,8437,8536,8607,8666,8724,8781,8840,8904,8975,9047,9119,9191,9263,9330,9398,9466,9525,9588,9652,9742,9833,9893,9959,10026,10092,10162,10226,10279,10346,10407,10474,10587,10645,10708,10773,10838,10913,10986,11058,11107,11168,11229,11290,11352,11416,11480,11544,11609,11672,11732,11793,11859,11918,11978,12040,12111,12171,12239,12813,12900,13153,13240,13328,13410,13493,13583,13674,15360,15418,15463,15529,15593,15650,15707,15761,17941,17989,18038,18089,18327,18710,18759,19014,20171,20506,20568,20628,20685,20955,21025,21103,21157,21227,21312,21360,21406,21467,21530,21596,21660,21731,21794,21859,21923,21984,22045,22097,22170,22244,22313,22388,22462,22536,22677,22747,24319,24705,24795,24883,24979,25069,25651,25740,25987,26268,26520,26805,27198,27675,27897,28119,28395,28622,28852,29082,29312,29542,29769,30188,30414,30839,31069,31497,31716,31999,32207,32338,32565,32991,33216,33643,33864,34289,34409,34685,34986,35310,35601,35915,36052,36183,36288,36530,36697,36901,37109,37380,37492,37604,37709,37826,38040,38186,38326,38412,38760,38848,39094,39512,39761,39843,39941,40533,40633,40885,41309,41564,41658,41747,41984,44008,44250,44352,44605,46761,57293,58809,69440,70968,72725,73351,73771,74832,76097,76353,76589,77136,77630,78235,78433,79013,79577,79952,80070,80608,80765,80961,81234,81490,81660,81801,81865,82230,82597,83273,83537,83875,84228,84322,84508,84814,85076,85201,85328,85567,85778,85897,86090,86267,86722,86903,87025,87284,87397,87584,87686,87793,87922,88197,88705,89201,90078,90372,90942,91091,91823,91995,92079,92415,92507,92785,99819,105208,105270,105848,106432,106523,114492,114721,114881,115033,115204,115370,115539,115706,115869,116112,116282,116455,116626,116900,117099,117304,117634,117718,117814,117910,118008,118108,118210,118312,118414,118516,118618,118718,118814,118926,119055,119178,119309,119440,119538,119652,119746,119886,120020,120116,120228,120328,120444,120540,120652,120752,120892,121028,121192,121322,121480,121630,121771,121915,122050,122162,122312,122440,122568,122704,122836,122966,123096,123208,123348,124252,124396,124534,124600,124690,124766,124870,124960,125062,125170,125278,125378,125458,125550,125648,125758,125836,125942,126034,126138,126248,126370,126533,126690,126770,126870,126960,127070,127160,127401,127495,127601,127693,127793,127905,128019,128135,128251,128345,128459,128571,128673,128793,128915,128997,129101,129221,129347,129445,129539,129627,129739,129855,129977,130089,130264,130380,130466,130558,130670,130794,130861,130987,131055,131183,131327,131455,131524,131619,131734,131847,131946,132055,132166,132277,132378,132483,132583,132713,132804,132927,133021,133133,133219,133323,133419,133507,133625,133729,133833,133959,134047,134155,134255,134345,134455,134539,134641,134725,134779,134843,134949,135035,135145,135229,135349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a81ffe6d33e2752270c36443a3adad5d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "63,110,246,247,248,249,250,251,252,309,310,311,350,351,388,398,399,400,405,406,407,1480,1664,1667,1673,1679,1682,1688,1692,1695,1702,1708,1711,1717,1722,1727,1734,1736,1742,1748,1756,1761,1768,1773,1779,1783,1790,1794,1800,1806,1809,1813,1814", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,5457,14880,14944,14999,15067,15134,15199,15256,18511,18559,18607,20690,20753,23346,24030,24087,24131,24395,24534,24584,92790,106528,106633,106878,107216,107362,107702,107914,108077,108484,108822,108945,109284,109523,109780,110151,110211,110549,110835,111284,111576,111964,112269,112613,112858,113188,113395,113663,113936,114080,114281,114328", "endLines": "63,110,246,247,248,249,250,251,252,309,310,311,350,351,388,398,399,402,405,406,407,1496,1666,1672,1678,1681,1687,1691,1694,1701,1707,1710,1716,1721,1726,1733,1735,1741,1747,1755,1760,1767,1772,1778,1782,1789,1793,1799,1805,1808,1812,1813,1814", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "2268,5521,14939,14994,15062,15129,15194,15251,15308,18554,18602,18663,20748,20811,23379,24082,24126,24266,24529,24579,24627,94223,106628,106873,107211,107357,107697,107909,108072,108479,108817,108940,109279,109518,109775,110146,110206,110544,110830,111279,111571,111959,112264,112608,112853,113183,113390,113658,113931,114075,114276,114323,114379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1d7be2e521d9c28fdcbcdffdf414b0f3\\transformed\\jetified-media3-exoplayer-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "389,390,391,392,393,394,395,396,397", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23384,23454,23516,23581,23645,23722,23787,23877,23961", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "23449,23511,23576,23640,23717,23782,23872,23956,24025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b3d21e6cdc9743ab017bee95b0fa303a\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "314,342", "startColumns": "4,4", "startOffsets": "18764,20219", "endColumns": "41,59", "endOffsets": "18801,20274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4ad25238eec4b9863d3220d3276a5601\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "343", "startColumns": "4", "startOffsets": "20279", "endColumns": "53", "endOffsets": "20328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\89805695e4625e4473cd4427b22fa2c0\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3,2081", "startColumns": "4,4", "startOffsets": "164,135493", "endLines": "3,2083", "endColumns": "60,12", "endOffsets": "220,135633"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.cat_player.app-mergeDebugResources-40:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\90c119ac56a5e8a0ea1963ca6cb61c3b\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2202,2218,2224,3514,3530", "startColumns": "4,4,4,4,4", "startOffsets": "139681,140106,140284,185054,185465", "endLines": "2217,2223,2233,3529,3533", "endColumns": "24,24,24,24,24", "endOffsets": "140101,140279,140563,185460,185587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\61778a0f7aa3fa286a534061d48ac657\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "29,70,71,84,85,108,109,209,210,211,212,213,214,215,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,306,307,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,352,381,382,383,384,385,386,387,404,1919,1920,1924,1925,1929,2072,2073,2729,2746,2916,2951,2981,3014", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,3841,3906,5325,5394,12244,12314,12382,12454,12524,12585,12659,13902,13963,14024,14086,14150,14212,14273,14341,14441,14501,14567,14640,14709,14766,14818,15766,15838,15914,15979,16038,16097,16157,16217,16277,16337,16397,16457,16517,16577,16637,16697,16756,16816,16876,16936,16996,17056,17116,17176,17236,17296,17356,17415,17475,17535,17594,17653,17712,17771,17830,18398,18433,19019,19074,19137,19192,19250,19308,19369,19432,19489,19540,19590,19651,19708,19774,19808,19843,20816,22835,22902,22974,23043,23112,23186,23258,24324,123003,123120,123321,123431,123632,135004,135076,156660,157264,165099,166905,167905,168587", "endLines": "29,70,71,84,85,108,109,209,210,211,212,213,214,215,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,306,307,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,352,381,382,383,384,385,386,387,404,1919,1923,1924,1928,1929,2072,2073,2734,2755,2950,2971,3013,3019", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,3901,3967,5389,5452,12309,12377,12449,12519,12580,12654,12727,13958,14019,14081,14145,14207,14268,14336,14436,14496,14562,14635,14704,14761,14813,14875,15833,15909,15974,16033,16092,16152,16212,16272,16332,16392,16452,16512,16572,16632,16692,16751,16811,16871,16931,16991,17051,17111,17171,17231,17291,17351,17410,17470,17530,17589,17648,17707,17766,17825,17884,18428,18463,19069,19132,19187,19245,19303,19364,19427,19484,19535,19585,19646,19703,19769,19803,19838,19873,20881,22897,22969,23038,23107,23181,23253,23341,24390,123115,123316,123426,123627,123756,135071,135138,156858,157560,166900,167581,168582,168749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\937e01ffe8e3e44dd70443b1b5c16c96\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "301,317,345,2972,2977", "startColumns": "4,4,4,4,4", "startOffsets": "18154,18908,20383,167586,167756", "endLines": "301,317,345,2976,2980", "endColumns": "56,64,63,24,24", "endOffsets": "18206,18968,20442,167751,167900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4a74e5ac7357230d5a0fd7c6e51811c9\\transformed\\jetified-lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "344", "startColumns": "4", "startOffsets": "20333", "endColumns": "49", "endOffsets": "20378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8b30fecce617e5aea6acd97724cab18f\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,300,2181,2187,3475,3483,3498", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,18094,138811,139006,183504,183786,184400", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,300,2186,2191,3482,3497,3513", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,18149,139001,139159,183781,184395,185049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9818094924ae8d5efe0adc37cab3bd38\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "380", "startColumns": "4", "startOffsets": "22752", "endColumns": "82", "endOffsets": "22830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\51e2db322baefcf987b45c52f853c125\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "218,219,220,228,229,230,305,3395", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "12905,12964,13012,13679,13754,13830,18332,180862", "endLines": "218,219,220,228,229,230,305,3414", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "12959,13007,13063,13749,13825,13897,18393,181652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f3570528e38cbbfd6737e6cd70922de8\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "302,303,308,315,316,335,336,337,338,339", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "18211,18251,18468,18806,18861,19878,19932,19984,20033,20094", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "18246,18293,18506,18856,18903,19927,19979,20028,20089,20139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\78555cd4f200d0089ab1a65fd6f62ef1\\transformed\\jetified-lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "341", "startColumns": "4", "startOffsets": "20176", "endColumns": "42", "endOffsets": "20214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\37e60f48f83182062f6e03d9950badc4\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,82,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,216,217,221,222,223,224,225,226,227,253,254,255,256,257,258,259,260,296,297,298,299,304,312,313,318,340,346,347,348,349,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,403,408,409,410,411,412,413,421,422,426,430,434,439,445,452,456,460,465,469,473,477,481,485,489,495,499,505,509,515,519,524,528,531,535,541,545,551,555,561,564,568,572,576,580,584,585,586,587,590,593,596,599,603,604,605,606,607,610,612,614,616,621,622,626,632,636,637,639,650,651,655,661,665,666,667,671,698,702,703,707,735,905,931,1102,1128,1159,1167,1173,1187,1209,1214,1219,1229,1238,1247,1251,1258,1266,1273,1274,1283,1286,1289,1293,1297,1301,1304,1305,1310,1315,1325,1330,1337,1343,1344,1347,1351,1356,1358,1360,1363,1366,1368,1372,1375,1382,1385,1388,1392,1394,1398,1400,1402,1404,1408,1416,1424,1436,1442,1451,1454,1465,1468,1469,1474,1475,1497,1566,1636,1637,1647,1656,1808,1810,1814,1817,1820,1823,1826,1829,1832,1835,1839,1842,1845,1848,1852,1855,1859,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1885,1887,1888,1889,1890,1891,1892,1893,1894,1896,1897,1899,1900,1902,1904,1905,1907,1908,1909,1910,1911,1912,1914,1915,1916,1917,1918,1930,1932,1934,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1950,1951,1952,1953,1954,1955,1957,1961,1965,1966,1967,1968,1969,1970,1974,1975,1976,1977,1979,1981,1983,1985,1987,1988,1989,1990,1992,1994,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2010,2011,2012,2013,2015,2017,2018,2020,2021,2023,2025,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2040,2041,2042,2043,2045,2046,2047,2048,2049,2051,2053,2055,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2077,2152,2155,2158,2161,2175,2192,2234,2263,2290,2299,2361,2725,2756,2894,3020,3044,3050,3066,3087,3211,3239,3245,3389,3415,3463,3534,3634,3654,3709,3721,3747", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3726,3783,3972,4046,4121,4186,4252,4312,4373,4445,4518,4585,4653,4712,4771,4830,4889,4948,5002,5056,5109,5163,5217,5271,5526,5600,5679,5752,5826,5897,5969,6041,6114,6171,6229,6302,6376,6450,6525,6597,6670,6740,6811,6871,6932,7001,7070,7140,7214,7290,7354,7431,7507,7584,7649,7718,7795,7870,7939,8007,8084,8150,8211,8308,8373,8442,8541,8612,8671,8729,8786,8845,8909,8980,9052,9124,9196,9268,9335,9403,9471,9530,9593,9657,9747,9838,9898,9964,10031,10097,10167,10231,10284,10351,10412,10479,10592,10650,10713,10778,10843,10918,10991,11063,11112,11173,11234,11295,11357,11421,11485,11549,11614,11677,11737,11798,11864,11923,11983,12045,12116,12176,12732,12818,13068,13158,13245,13333,13415,13498,13588,15313,15365,15423,15468,15534,15598,15655,15712,17889,17946,17994,18043,18298,18668,18715,18973,20144,20447,20511,20573,20633,20886,20960,21030,21108,21162,21232,21317,21365,21411,21472,21535,21601,21665,21736,21799,21864,21928,21989,22050,22102,22175,22249,22318,22393,22467,22541,22682,24271,24632,24710,24800,24888,24984,25074,25656,25745,25992,26273,26525,26810,27203,27680,27902,28124,28400,28627,28857,29087,29317,29547,29774,30193,30419,30844,31074,31502,31721,32004,32212,32343,32570,32996,33221,33648,33869,34294,34414,34690,34991,35315,35606,35920,36057,36188,36293,36535,36702,36906,37114,37385,37497,37609,37714,37831,38045,38191,38331,38417,38765,38853,39099,39517,39766,39848,39946,40538,40638,40890,41314,41569,41663,41752,41989,44013,44255,44357,44610,46766,57298,58814,69445,70973,72730,73356,73776,74837,76102,76358,76594,77141,77635,78240,78438,79018,79582,79957,80075,80613,80770,80966,81239,81495,81665,81806,81870,82235,82602,83278,83542,83880,84233,84327,84513,84819,85081,85206,85333,85572,85783,85902,86095,86272,86727,86908,87030,87289,87402,87589,87691,87798,87927,88202,88710,89206,90083,90377,90947,91096,91828,92000,92084,92420,92512,94228,99474,104863,104925,105503,106087,114034,114147,114376,114536,114688,114859,115025,115194,115361,115524,115767,115937,116110,116281,116555,116754,116959,117289,117373,117469,117565,117663,117763,117865,117967,118069,118171,118273,118373,118469,118581,118710,118833,118964,119095,119193,119307,119401,119541,119675,119771,119883,119983,120099,120195,120307,120407,120547,120683,120847,120977,121135,121285,121426,121570,121705,121817,121967,122095,122223,122359,122491,122621,122751,122863,123761,123907,124051,124189,124255,124345,124421,124525,124615,124717,124825,124933,125033,125113,125205,125303,125413,125491,125597,125689,125793,125903,126025,126188,126345,126425,126525,126615,126725,126815,127056,127150,127256,127348,127448,127560,127674,127790,127906,128000,128114,128226,128328,128448,128570,128652,128756,128876,129002,129100,129194,129282,129394,129510,129632,129744,129919,130035,130121,130213,130325,130449,130516,130642,130710,130838,130982,131110,131179,131274,131389,131502,131601,131710,131821,131932,132033,132138,132238,132368,132459,132582,132676,132788,132874,132978,133074,133162,133280,133384,133488,133614,133702,133810,133910,134000,134110,134194,134296,134380,134434,134498,134604,134690,134800,134884,135288,137904,138022,138137,138217,138578,139164,140568,141912,143273,143661,146436,156525,157565,164378,168754,169505,169767,170299,170678,174956,175810,176039,180647,181657,183192,185592,189716,190460,192591,192931,194242", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,82,83,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,216,217,221,222,223,224,225,226,227,253,254,255,256,257,258,259,260,296,297,298,299,304,312,313,318,340,346,347,348,349,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,403,408,409,410,411,412,420,421,425,429,433,438,444,451,455,459,464,468,472,476,480,484,488,494,498,504,508,514,518,523,527,530,534,540,544,550,554,560,563,567,571,575,579,583,584,585,586,589,592,595,598,602,603,604,605,606,609,611,613,615,620,621,625,631,635,636,638,649,650,654,660,664,665,666,670,697,701,702,706,734,904,930,1101,1127,1158,1166,1172,1186,1208,1213,1218,1228,1237,1246,1250,1257,1265,1272,1273,1282,1285,1288,1292,1296,1300,1303,1304,1309,1314,1324,1329,1336,1342,1343,1346,1350,1355,1357,1359,1362,1365,1367,1371,1374,1381,1384,1387,1391,1393,1397,1399,1401,1403,1407,1415,1423,1435,1441,1450,1453,1464,1467,1468,1473,1474,1479,1565,1635,1636,1646,1655,1656,1809,1813,1816,1819,1822,1825,1828,1831,1834,1838,1841,1844,1847,1851,1854,1858,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1884,1886,1887,1888,1889,1890,1891,1892,1893,1895,1896,1898,1899,1901,1903,1904,1906,1907,1908,1909,1910,1911,1913,1914,1915,1916,1917,1918,1931,1933,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1949,1950,1951,1952,1953,1954,1956,1960,1964,1965,1966,1967,1968,1969,1973,1974,1975,1976,1978,1980,1982,1984,1986,1987,1988,1989,1991,1993,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2009,2010,2011,2012,2014,2016,2017,2019,2020,2022,2024,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2039,2040,2041,2042,2044,2045,2046,2047,2048,2050,2052,2054,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2151,2154,2157,2160,2174,2180,2201,2262,2289,2298,2360,2719,2728,2783,2911,3043,3049,3055,3086,3210,3230,3244,3248,3394,3449,3474,3599,3653,3708,3720,3746,3753", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,3778,3836,4041,4116,4181,4247,4307,4368,4440,4513,4580,4648,4707,4766,4825,4884,4943,4997,5051,5104,5158,5212,5266,5320,5595,5674,5747,5821,5892,5964,6036,6109,6166,6224,6297,6371,6445,6520,6592,6665,6735,6806,6866,6927,6996,7065,7135,7209,7285,7349,7426,7502,7579,7644,7713,7790,7865,7934,8002,8079,8145,8206,8303,8368,8437,8536,8607,8666,8724,8781,8840,8904,8975,9047,9119,9191,9263,9330,9398,9466,9525,9588,9652,9742,9833,9893,9959,10026,10092,10162,10226,10279,10346,10407,10474,10587,10645,10708,10773,10838,10913,10986,11058,11107,11168,11229,11290,11352,11416,11480,11544,11609,11672,11732,11793,11859,11918,11978,12040,12111,12171,12239,12813,12900,13153,13240,13328,13410,13493,13583,13674,15360,15418,15463,15529,15593,15650,15707,15761,17941,17989,18038,18089,18327,18710,18759,19014,20171,20506,20568,20628,20685,20955,21025,21103,21157,21227,21312,21360,21406,21467,21530,21596,21660,21731,21794,21859,21923,21984,22045,22097,22170,22244,22313,22388,22462,22536,22677,22747,24319,24705,24795,24883,24979,25069,25651,25740,25987,26268,26520,26805,27198,27675,27897,28119,28395,28622,28852,29082,29312,29542,29769,30188,30414,30839,31069,31497,31716,31999,32207,32338,32565,32991,33216,33643,33864,34289,34409,34685,34986,35310,35601,35915,36052,36183,36288,36530,36697,36901,37109,37380,37492,37604,37709,37826,38040,38186,38326,38412,38760,38848,39094,39512,39761,39843,39941,40533,40633,40885,41309,41564,41658,41747,41984,44008,44250,44352,44605,46761,57293,58809,69440,70968,72725,73351,73771,74832,76097,76353,76589,77136,77630,78235,78433,79013,79577,79952,80070,80608,80765,80961,81234,81490,81660,81801,81865,82230,82597,83273,83537,83875,84228,84322,84508,84814,85076,85201,85328,85567,85778,85897,86090,86267,86722,86903,87025,87284,87397,87584,87686,87793,87922,88197,88705,89201,90078,90372,90942,91091,91823,91995,92079,92415,92507,92785,99469,104858,104920,105498,106082,106173,114142,114371,114531,114683,114854,115020,115189,115356,115519,115762,115932,116105,116276,116550,116749,116954,117284,117368,117464,117560,117658,117758,117860,117962,118064,118166,118268,118368,118464,118576,118705,118828,118959,119090,119188,119302,119396,119536,119670,119766,119878,119978,120094,120190,120302,120402,120542,120678,120842,120972,121130,121280,121421,121565,121700,121812,121962,122090,122218,122354,122486,122616,122746,122858,122998,123902,124046,124184,124250,124340,124416,124520,124610,124712,124820,124928,125028,125108,125200,125298,125408,125486,125592,125684,125788,125898,126020,126183,126340,126420,126520,126610,126720,126810,127051,127145,127251,127343,127443,127555,127669,127785,127901,127995,128109,128221,128323,128443,128565,128647,128751,128871,128997,129095,129189,129277,129389,129505,129627,129739,129914,130030,130116,130208,130320,130444,130511,130637,130705,130833,130977,131105,131174,131269,131384,131497,131596,131705,131816,131927,132028,132133,132233,132363,132454,132577,132671,132783,132869,132973,133069,133157,133275,133379,133483,133609,133697,133805,133905,133995,134105,134189,134291,134375,134429,134493,134599,134685,134795,134879,134999,137899,138017,138132,138212,138573,138806,139676,141907,143268,143656,146431,156335,156655,158917,164945,169500,169762,169962,170673,174951,175557,176034,176185,180857,182735,183499,188613,190455,192586,192926,194237,194440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a81ffe6d33e2752270c36443a3adad5d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,110,246,247,248,249,250,251,252,309,310,311,350,351,388,398,399,400,405,406,407,1480,1657,1660,1666,1672,1675,1681,1685,1688,1695,1701,1704,1710,1715,1720,1727,1729,1735,1741,1749,1754,1761,1766,1772,1776,1783,1787,1793,1799,1802,1806,1807,2720,2735,2874,2912,3056,3231,3249,3313,3323,3333,3340,3346,3450,3600,3617", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,5457,14880,14944,14999,15067,15134,15199,15256,18511,18559,18607,20690,20753,23346,24030,24087,24131,24395,24534,24584,92790,106178,106283,106528,106866,107012,107352,107564,107727,108134,108472,108595,108934,109173,109430,109801,109861,110199,110485,110934,111226,111614,111919,112263,112508,112838,113045,113313,113586,113730,113931,113978,156340,156863,163649,164950,169967,175562,176190,178115,178397,178702,178964,179224,182740,188618,189148", "endLines": "63,110,246,247,248,249,250,251,252,309,310,311,350,351,388,398,399,402,405,406,407,1496,1659,1665,1671,1674,1680,1684,1687,1694,1700,1703,1709,1714,1719,1726,1728,1734,1740,1748,1753,1760,1765,1771,1775,1782,1786,1792,1798,1801,1805,1806,1807,2724,2745,2893,2915,3065,3238,3312,3322,3332,3339,3345,3388,3462,3616,3633", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,5521,14939,14994,15062,15129,15194,15251,15308,18554,18602,18663,20748,20811,23379,24082,24126,24266,24529,24579,24627,94223,106278,106523,106861,107007,107347,107559,107722,108129,108467,108590,108929,109168,109425,109796,109856,110194,110480,110929,111221,111609,111914,112258,112503,112833,113040,113308,113581,113725,113926,113973,114029,156520,157259,164373,165094,170294,175805,178110,178392,178697,178959,179219,180642,183187,189143,189711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1d7be2e521d9c28fdcbcdffdf414b0f3\\transformed\\jetified-media3-exoplayer-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "389,390,391,392,393,394,395,396,397", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23384,23454,23516,23581,23645,23722,23787,23877,23961", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "23449,23511,23576,23640,23717,23782,23872,23956,24025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b3d21e6cdc9743ab017bee95b0fa303a\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "314,342", "startColumns": "4,4", "startOffsets": "18764,20219", "endColumns": "41,59", "endOffsets": "18801,20274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4ad25238eec4b9863d3220d3276a5601\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "343", "startColumns": "4", "startOffsets": "20279", "endColumns": "53", "endOffsets": "20328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\89805695e4625e4473cd4427b22fa2c0\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2074,2784,2790", "startColumns": "4,4,4,4", "startOffsets": "164,135143,158922,159133", "endLines": "3,2076,2789,2873", "endColumns": "60,12,24,24", "endOffsets": "220,135283,159128,163644"}}]}]}