-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:8:5-32:19
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:8:5-32:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\9818094924ae8d5efe0adc37cab3bd38\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\9818094924ae8d5efe0adc37cab3bd38\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6aa9f590154c958508002163ad3d96a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6aa9f590154c958508002163ad3d96a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:9:9-35
	android:icon
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:11:9-43
	android:name
		INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:10:9-42
manifest
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:1:1-33:12
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:1:1-33:12
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:1:1-33:12
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:1:1-33:12
MERGED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\New folder (2)\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:wakelock_plus] C:\Users\<USER>\Desktop\New folder (2)\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\New folder (2)\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\New folder (2)\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\New folder (2)\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:video_player_android] C:\Users\<USER>\Desktop\New folder (2)\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a81ffe6d33e2752270c36443a3adad5d\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\37e60f48f83182062f6e03d9950badc4\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c78d8b4b033817b31c38bf42c003789\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\937e01ffe8e3e44dd70443b1b5c16c96\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\c2ef6cb20fc1ea8dd6edcb090b2d311b\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b3d21e6cdc9743ab017bee95b0fa303a\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e2db322baefcf987b45c52f853c125\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\36672d21f4dcc608b91732c3c4aa0b9a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1731f9a16e8465465b80c2eb1f87c00\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8d213cdc073e56da513c6aa783c9693\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\843ad231717124e1b3667a6181092054\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4ad25238eec4b9863d3220d3276a5601\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5a8d7fd7de86b91da633fbd814577e28\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ccbdf5cbfb934abf88591c23ad6d8f9a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\4a74e5ac7357230d5a0fd7c6e51811c9\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ce93a68822f6fee44e5b282987c394c0\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\f50ba10249b2c7c4396dc63d98c24c58\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f6c5b76e412b92812b3c3514ace1bd\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\311a1186137f72f56fba96cafd2d348c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\78555cd4f200d0089ab1a65fd6f62ef1\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b5f7b4667442a56a38a60724078009b1\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\5cb7b281a249ce04b3d549762dee98cb\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1f5bcc5843d224d9af6be9f6fe9619b5\transformed\jetified-media3-extractor-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\df53d2a384a293036928e41fa71d884a\transformed\jetified-media3-container-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\5cddfb5ef39095f64b3accd3ab48bc1e\transformed\jetified-media3-datasource-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\524ec3b3e15c8c88fe8af2afa1577d5a\transformed\jetified-media3-decoder-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\6a9be058ad0bbf36449c943fb40ff27f\transformed\jetified-media3-database-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a7f7d5fcf997cde65123df4305b7ab3\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\b95bf6cf3f906d05d77f3631f8b389b1\transformed\jetified-media3-exoplayer-hls-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\9ee35dbef481e5f1a4e2c7099b0d5466\transformed\jetified-media3-exoplayer-dash-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\cea4b1deec99bc7e4da30ed0f382c68d\transformed\jetified-media3-exoplayer-rtsp-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\dc782255ee4236600fda657be2e9ef9b\transformed\jetified-media3-exoplayer-smoothstreaming-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d7be2e521d9c28fdcbcdffdf414b0f3\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3353e932ac5d61b8f0f4fd0db8977849\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c65c516367dc20e9a584e49024d31\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b3df3287508bedfd95518f9dc46ccbb\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\90c119ac56a5e8a0ea1963ca6cb61c3b\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcf7c4526ccfd548897c739ffbb56b3e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\89805695e4625e4473cd4427b22fa2c0\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b363bfcbfe6860de0355144a63e574e4\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3570528e38cbbfd6737e6cd70922de8\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b49a750265eda2de905163de4ce8591e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\080b8ca5a638e8fba0aea78910d434d8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\96687d622fba5b1250b5d46dab269ff5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7d8f06d5a933c07aa0ac9b870663a15d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\61f3d98e8210bb9de809480a52888e3f\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\9818094924ae8d5efe0adc37cab3bd38\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\88c24a53f81a7ed501d3b3a81c82286d\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7e24f553573e2b98a72bef75e88e6d9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6aa9f590154c958508002163ad3d96a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0e6cbfdb344c6436f505e2be17191ed7\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fb2cd0d9fddc1142d484174a4bf497b4\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0c86624de27a0954c3a2ab8097281460\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\f6ba2c1c68b9e52c971539b0032b5af0\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8de26bf2646963605b65f7e789bbb642\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d01c3ea74f25f4c320e4fdfa1530aa57\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d5ae4b5ca8ec4273da409e2fac06ec\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\961c424edf245753335db5a91a6bc93b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2cd857903f35dffafd18f05da5a2c29\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b30173ecdc5ab10e91e931a309f4ead5\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-4\30592f61ad3ccd0111a612c8bbe9e94f\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:4:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:4:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:5:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:5:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:6:5-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:6:22-79
activity#com.example.cat_player.MainActivity
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:12:9-28:20
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:15:13-43
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:18:13-47
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:19:13-55
	android:exported
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:14:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:17:13-163
	android:theme
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:16:13-47
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:13:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:20:13-23:17
	android:resource
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:22:15-52
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:21:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:24:13-27:29
	android:autoVerify
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:24:28-53
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:25:17-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:26:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:26:27-74
meta-data#flutterEmbedding
ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:29:9-31:33
	android:value
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:31:13-30
	android:name
		ADDED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:30:13-44
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
MERGED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\New folder (2)\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\New folder (2)\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] C:\Users\<USER>\Desktop\New folder (2)\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] C:\Users\<USER>\Desktop\New folder (2)\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\New folder (2)\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\New folder (2)\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\New folder (2)\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\New folder (2)\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\New folder (2)\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Desktop\New folder (2)\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] C:\Users\<USER>\Desktop\New folder (2)\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] C:\Users\<USER>\Desktop\New folder (2)\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a81ffe6d33e2752270c36443a3adad5d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\a81ffe6d33e2752270c36443a3adad5d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\37e60f48f83182062f6e03d9950badc4\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\37e60f48f83182062f6e03d9950badc4\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c78d8b4b033817b31c38bf42c003789\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c78d8b4b033817b31c38bf42c003789\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\937e01ffe8e3e44dd70443b1b5c16c96\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\937e01ffe8e3e44dd70443b1b5c16c96\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\c2ef6cb20fc1ea8dd6edcb090b2d311b\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\c2ef6cb20fc1ea8dd6edcb090b2d311b\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b3d21e6cdc9743ab017bee95b0fa303a\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\b3d21e6cdc9743ab017bee95b0fa303a\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e2db322baefcf987b45c52f853c125\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e2db322baefcf987b45c52f853c125\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\36672d21f4dcc608b91732c3c4aa0b9a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\36672d21f4dcc608b91732c3c4aa0b9a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1731f9a16e8465465b80c2eb1f87c00\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1731f9a16e8465465b80c2eb1f87c00\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8d213cdc073e56da513c6aa783c9693\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8d213cdc073e56da513c6aa783c9693\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\843ad231717124e1b3667a6181092054\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\843ad231717124e1b3667a6181092054\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4ad25238eec4b9863d3220d3276a5601\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4ad25238eec4b9863d3220d3276a5601\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5a8d7fd7de86b91da633fbd814577e28\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5a8d7fd7de86b91da633fbd814577e28\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ccbdf5cbfb934abf88591c23ad6d8f9a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ccbdf5cbfb934abf88591c23ad6d8f9a\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\4a74e5ac7357230d5a0fd7c6e51811c9\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\4a74e5ac7357230d5a0fd7c6e51811c9\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ce93a68822f6fee44e5b282987c394c0\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ce93a68822f6fee44e5b282987c394c0\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\f50ba10249b2c7c4396dc63d98c24c58\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\f50ba10249b2c7c4396dc63d98c24c58\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f6c5b76e412b92812b3c3514ace1bd\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\e4f6c5b76e412b92812b3c3514ace1bd\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\311a1186137f72f56fba96cafd2d348c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\311a1186137f72f56fba96cafd2d348c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\78555cd4f200d0089ab1a65fd6f62ef1\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\78555cd4f200d0089ab1a65fd6f62ef1\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b5f7b4667442a56a38a60724078009b1\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\b5f7b4667442a56a38a60724078009b1\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\5cb7b281a249ce04b3d549762dee98cb\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\5cb7b281a249ce04b3d549762dee98cb\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1f5bcc5843d224d9af6be9f6fe9619b5\transformed\jetified-media3-extractor-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1f5bcc5843d224d9af6be9f6fe9619b5\transformed\jetified-media3-extractor-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\df53d2a384a293036928e41fa71d884a\transformed\jetified-media3-container-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\df53d2a384a293036928e41fa71d884a\transformed\jetified-media3-container-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\5cddfb5ef39095f64b3accd3ab48bc1e\transformed\jetified-media3-datasource-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\5cddfb5ef39095f64b3accd3ab48bc1e\transformed\jetified-media3-datasource-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\524ec3b3e15c8c88fe8af2afa1577d5a\transformed\jetified-media3-decoder-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\524ec3b3e15c8c88fe8af2afa1577d5a\transformed\jetified-media3-decoder-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\6a9be058ad0bbf36449c943fb40ff27f\transformed\jetified-media3-database-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\6a9be058ad0bbf36449c943fb40ff27f\transformed\jetified-media3-database-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a7f7d5fcf997cde65123df4305b7ab3\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a7f7d5fcf997cde65123df4305b7ab3\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\b95bf6cf3f906d05d77f3631f8b389b1\transformed\jetified-media3-exoplayer-hls-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\b95bf6cf3f906d05d77f3631f8b389b1\transformed\jetified-media3-exoplayer-hls-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\9ee35dbef481e5f1a4e2c7099b0d5466\transformed\jetified-media3-exoplayer-dash-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\9ee35dbef481e5f1a4e2c7099b0d5466\transformed\jetified-media3-exoplayer-dash-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\cea4b1deec99bc7e4da30ed0f382c68d\transformed\jetified-media3-exoplayer-rtsp-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\cea4b1deec99bc7e4da30ed0f382c68d\transformed\jetified-media3-exoplayer-rtsp-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\dc782255ee4236600fda657be2e9ef9b\transformed\jetified-media3-exoplayer-smoothstreaming-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\dc782255ee4236600fda657be2e9ef9b\transformed\jetified-media3-exoplayer-smoothstreaming-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d7be2e521d9c28fdcbcdffdf414b0f3\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d7be2e521d9c28fdcbcdffdf414b0f3\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3353e932ac5d61b8f0f4fd0db8977849\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3353e932ac5d61b8f0f4fd0db8977849\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c65c516367dc20e9a584e49024d31\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\622c65c516367dc20e9a584e49024d31\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b3df3287508bedfd95518f9dc46ccbb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b3df3287508bedfd95518f9dc46ccbb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\90c119ac56a5e8a0ea1963ca6cb61c3b\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\90c119ac56a5e8a0ea1963ca6cb61c3b\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcf7c4526ccfd548897c739ffbb56b3e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dcf7c4526ccfd548897c739ffbb56b3e\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\89805695e4625e4473cd4427b22fa2c0\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\89805695e4625e4473cd4427b22fa2c0\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b363bfcbfe6860de0355144a63e574e4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b363bfcbfe6860de0355144a63e574e4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3570528e38cbbfd6737e6cd70922de8\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3570528e38cbbfd6737e6cd70922de8\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b49a750265eda2de905163de4ce8591e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b49a750265eda2de905163de4ce8591e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\080b8ca5a638e8fba0aea78910d434d8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\080b8ca5a638e8fba0aea78910d434d8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\96687d622fba5b1250b5d46dab269ff5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\96687d622fba5b1250b5d46dab269ff5\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7d8f06d5a933c07aa0ac9b870663a15d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7d8f06d5a933c07aa0ac9b870663a15d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\61f3d98e8210bb9de809480a52888e3f\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\61f3d98e8210bb9de809480a52888e3f\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\9818094924ae8d5efe0adc37cab3bd38\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\9818094924ae8d5efe0adc37cab3bd38\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\88c24a53f81a7ed501d3b3a81c82286d\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\88c24a53f81a7ed501d3b3a81c82286d\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7e24f553573e2b98a72bef75e88e6d9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7e24f553573e2b98a72bef75e88e6d9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6aa9f590154c958508002163ad3d96a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6aa9f590154c958508002163ad3d96a5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0e6cbfdb344c6436f505e2be17191ed7\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0e6cbfdb344c6436f505e2be17191ed7\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fb2cd0d9fddc1142d484174a4bf497b4\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\fb2cd0d9fddc1142d484174a4bf497b4\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0c86624de27a0954c3a2ab8097281460\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-4\0c86624de27a0954c3a2ab8097281460\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\f6ba2c1c68b9e52c971539b0032b5af0\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\f6ba2c1c68b9e52c971539b0032b5af0\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8de26bf2646963605b65f7e789bbb642\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8de26bf2646963605b65f7e789bbb642\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d01c3ea74f25f4c320e4fdfa1530aa57\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d01c3ea74f25f4c320e4fdfa1530aa57\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d5ae4b5ca8ec4273da409e2fac06ec\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d5ae4b5ca8ec4273da409e2fac06ec\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\961c424edf245753335db5a91a6bc93b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\961c424edf245753335db5a91a6bc93b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2cd857903f35dffafd18f05da5a2c29\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2cd857903f35dffafd18f05da5a2c29\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b30173ecdc5ab10e91e931a309f4ead5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b30173ecdc5ab10e91e931a309f4ead5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-4\30592f61ad3ccd0111a612c8bbe9e94f\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-4\30592f61ad3ccd0111a612c8bbe9e94f\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml
queries
ADDED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
data
ADDED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
	android:mimeType
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:19-41
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\9818094924ae8d5efe0adc37cab3bd38\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\9818094924ae8d5efe0adc37cab3bd38\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a7f7d5fcf997cde65123df4305b7ab3\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d7be2e521d9c28fdcbcdffdf414b0f3\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d7be2e521d9c28fdcbcdffdf414b0f3\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a7f7d5fcf997cde65123df4305b7ab3\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:22-76
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.example.cat_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.cat_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
