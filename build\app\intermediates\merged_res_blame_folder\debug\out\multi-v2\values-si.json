{"logs": [{"outputFile": "com.example.cat_player.app-mergeDebugResources-41:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6168798dbbf969ae7d970b5aa2e72768\\transformed\\appcompat-1.1.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,916,1007,1099,1193,1287,1388,1481,1576,1670,1761,1852,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,86,90,91,93,93,100,92,94,93,90,90,82,108,103,97,109,99,106,158,98,80", "endOffsets": "216,323,430,513,618,734,824,911,1002,1094,1188,1282,1383,1476,1571,1665,1756,1847,1930,2039,2143,2241,2351,2451,2558,2717,2816,2897"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,916,1007,1099,1193,1287,1388,1481,1576,1670,1761,1852,1935,2044,2148,2246,2356,2456,2563,2722,4585", "endColumns": "115,106,106,82,104,115,89,86,90,91,93,93,100,92,94,93,90,90,82,108,103,97,109,99,106,158,98,80", "endOffsets": "216,323,430,513,618,734,824,911,1002,1094,1188,1282,1383,1476,1571,1665,1756,1847,1930,2039,2143,2241,2351,2451,2558,2717,2816,4661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c96807fae557de98e294925aceb41371\\transformed\\preference-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,264,339,482,651,743", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "176,259,334,477,646,738,825"}, "to": {"startLines": "36,46,47,48,51,52,53", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3553,4284,4367,4442,4767,4936,5028", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "3624,4362,4437,4580,4931,5023,5110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\062abdfdbf5519c5febe491baa33cd6d\\transformed\\jetified-media3-exoplayer-1.5.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3629,3705,3765,3828,3894,3970,4039,4128,4214", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "3700,3760,3823,3889,3965,4034,4123,4209,4279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\95fdb6225b6e552a46d9596694dbe4fc\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,50", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2821,2923,3026,3131,3236,3335,3439,4666", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "2918,3021,3126,3231,3330,3434,3548,4762"}}]}]}