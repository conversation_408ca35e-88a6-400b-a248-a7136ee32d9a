{"logs": [{"outputFile": "com.example.cat_player.app-mergeDebugResources-40:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\37e60f48f83182062f6e03d9950badc4\\transformed\\appcompat-1.1.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,880,971,1063,1155,1249,1350,1443,1538,1634,1725,1816,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,77,90,91,91,93,100,92,94,95,90,90,79,106,103,97,102,103,103,156,98,80", "endOffsets": "203,306,419,504,608,719,797,875,966,1058,1150,1244,1345,1438,1533,1629,1720,1811,1891,1998,2102,2200,2303,2407,2511,2668,2767,2848"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,880,971,1063,1155,1249,1350,1443,1538,1634,1725,1816,1896,2003,2107,2205,2308,2412,2516,2673,4509", "endColumns": "102,102,112,84,103,110,77,77,90,91,91,93,100,92,94,95,90,90,79,106,103,97,102,103,103,156,98,80", "endOffsets": "203,306,419,504,608,719,797,875,966,1058,1150,1244,1345,1438,1533,1629,1720,1811,1891,1998,2102,2200,2303,2407,2511,2668,2767,4585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a81ffe6d33e2752270c36443a3adad5d\\transformed\\preference-1.2.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,479,648,728", "endColumns": "69,85,79,137,168,79,77", "endOffsets": "170,256,336,474,643,723,801"}, "to": {"startLines": "36,46,47,48,51,52,53", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3478,4205,4291,4371,4691,4860,4940", "endColumns": "69,85,79,137,168,79,77", "endOffsets": "3543,4286,4366,4504,4855,4935,5013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\61778a0f7aa3fa286a534061d48ac657\\transformed\\core-1.15.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "29,30,31,32,33,34,35,50", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2772,2868,2971,3070,3168,3269,3367,4590", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "2863,2966,3065,3163,3264,3362,3473,4686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1d7be2e521d9c28fdcbcdffdf414b0f3\\transformed\\jetified-media3-exoplayer-1.5.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3548,3617,3678,3744,3809,3884,3954,4046,4133", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "3612,3673,3739,3804,3879,3949,4041,4128,4200"}}]}]}