1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.cat_player"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:4:5-80
12-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:4:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:5:5-81
13-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:5:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:6:5-82
14-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:6:22-79
15
16    <queries>
16-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
17        <intent>
17-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
18            <action android:name="android.intent.action.GET_CONTENT" />
18-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
18-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
19
20            <data android:mimeType="*/*" />
20-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
20-->[:file_picker] C:\Users\<USER>\Desktop\New folder (2)\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:19-41
21        </intent>
22    </queries>
23
24    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
24-->[androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a7f7d5fcf997cde65123df4305b7ab3\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:5-79
24-->[androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a7f7d5fcf997cde65123df4305b7ab3\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:22-76
25
26    <permission
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.example.cat_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.example.cat_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
31
32    <application
33        android:name="android.app.Application"
33-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:10:9-42
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\transforms-4\61778a0f7aa3fa286a534061d48ac657\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
35        android:debuggable="true"
36        android:extractNativeLibs="true"
37        android:icon="@mipmap/ic_launcher"
37-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:11:9-43
38        android:label="Cat Player" >
38-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:9:9-35
39        <activity
39-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:12:9-28:20
40            android:name="com.example.cat_player.MainActivity"
40-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:13:13-41
41            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
41-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:17:13-163
42            android:exported="true"
42-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:14:13-36
43            android:hardwareAccelerated="true"
43-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:18:13-47
44            android:launchMode="singleTop"
44-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:15:13-43
45            android:theme="@style/LaunchTheme"
45-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:16:13-47
46            android:windowSoftInputMode="adjustResize" >
46-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:19:13-55
47            <meta-data
47-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:20:13-23:17
48                android:name="io.flutter.embedding.android.NormalTheme"
48-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:21:15-70
49                android:resource="@style/NormalTheme" />
49-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:22:15-52
50
51            <intent-filter android:autoVerify="true" >
51-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:24:13-27:29
51-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:24:28-53
52                <action android:name="android.intent.action.MAIN" />
52-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:25:17-68
52-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:25:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:26:17-76
54-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:26:27-74
55            </intent-filter>
56        </activity>
57
58        <meta-data
58-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:29:9-31:33
59            android:name="flutterEmbedding"
59-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:30:13-44
60            android:value="2" />
60-->C:\Users\<USER>\Desktop\New folder (2)\android\app\src\main\AndroidManifest.xml:31:13-30
61
62        <provider
62-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
64            android:authorities="com.example.cat_player.androidx-startup"
64-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
67-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
68                android:value="androidx.startup" />
68-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\48e1b24a0328086c72ac0e33cdedb5ee\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
71                android:value="androidx.startup" />
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
72        </provider>
73
74        <uses-library
74-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
75            android:name="androidx.window.extensions"
75-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
76            android:required="false" />
76-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
77        <uses-library
77-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
78            android:name="androidx.window.sidecar"
78-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
79            android:required="false" />
79-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b30fecce617e5aea6acd97724cab18f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
80
81        <receiver
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
82            android:name="androidx.profileinstaller.ProfileInstallReceiver"
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
83            android:directBootAware="false"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
84            android:enabled="true"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
85            android:exported="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
86            android:permission="android.permission.DUMP" >
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
88                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
91                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
94                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
97                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\68488e278bd783ba196f450dfac4f859\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
98            </intent-filter>
99        </receiver>
100    </application>
101
102</manifest>
