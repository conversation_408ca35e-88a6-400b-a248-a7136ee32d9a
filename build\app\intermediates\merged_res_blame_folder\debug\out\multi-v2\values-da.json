{"logs": [{"outputFile": "com.example.cat_player.app-mergeDebugResources-41:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\062abdfdbf5519c5febe491baa33cd6d\\transformed\\jetified-media3-exoplayer-1.5.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "37,38,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3554,3626,3688,3752,3821,3898,3972,4072,4163", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "3621,3683,3747,3816,3893,3967,4067,4158,4226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c96807fae557de98e294925aceb41371\\transformed\\preference-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,490,659,739", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "170,258,337,485,654,734,811"}, "to": {"startLines": "36,46,47,48,51,52,53", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3484,4231,4319,4398,4726,4895,4975", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "3549,4314,4393,4541,4890,4970,5047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6168798dbbf969ae7d970b5aa2e72768\\transformed\\appcompat-1.1.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,868,959,1051,1144,1238,1333,1426,1521,1619,1710,1801,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,76,90,91,92,93,94,92,94,97,90,90,77,107,106,95,112,102,100,152,96,78", "endOffsets": "200,294,410,495,595,708,786,863,954,1046,1139,1233,1328,1421,1516,1614,1705,1796,1874,1982,2089,2185,2298,2401,2502,2655,2752,2831"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,868,959,1051,1144,1238,1333,1426,1521,1619,1710,1801,1879,1987,2094,2190,2303,2406,2507,2660,4546", "endColumns": "99,93,115,84,99,112,77,76,90,91,92,93,94,92,94,97,90,90,77,107,106,95,112,102,100,152,96,78", "endOffsets": "200,294,410,495,595,708,786,863,954,1046,1139,1233,1328,1421,1516,1614,1705,1796,1874,1982,2089,2185,2298,2401,2502,2655,2752,4620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\95fdb6225b6e552a46d9596694dbe4fc\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,50", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2757,2853,2955,3052,3150,3257,3366,4625", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "2848,2950,3047,3145,3252,3361,3479,4721"}}]}]}